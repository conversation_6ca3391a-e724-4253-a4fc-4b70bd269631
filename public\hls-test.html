<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS实时播放测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .stop-btn {
            background: #dc3545;
        }
        .stop-btn:hover {
            background: #c82333;
        }
        video {
            width: 100%;
            height: 500px;
            background: #000;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 HLS实时播放测试</h1>
        <p>专门用于测试连续播放功能的HLS版本</p>
        
        <div class="form-group">
            <label for="rtspUrl">RTSP地址:</label>
            <input type="text" id="rtspUrl" 
                   value="rtsp://admin:Yf.31306@************:554/Streaming/Channels/102"
                   placeholder="rtsp://username:password@ip:port/stream">
        </div>
        
        <div class="form-group">
            <label for="streamId">流ID:</label>
            <input type="text" id="streamId" value="camera102" placeholder="唯一标识符">
        </div>
        
        <button onclick="startHLSStream()">🚀 启动HLS流</button>
        <button onclick="stopStream()" class="stop-btn">⏹️ 停止流</button>
        
        <div id="status"></div>
        
        <video id="video" controls autoplay muted>
            您的浏览器不支持视频播放
        </video>
        
        <div class="logs" id="logs">
            <div>📋 操作日志:</div>
        </div>
        
        <div class="info">
            <h3>📝 说明:</h3>
            <ul>
                <li><strong>HLS优势:</strong> 更好的兼容性，支持连续播放</li>
                <li><strong>延迟:</strong> 约6-10秒（比FLV稍高，但更稳定）</li>
                <li><strong>片段:</strong> 每2秒生成一个视频片段</li>
                <li><strong>缓存:</strong> 保持6个片段，自动删除旧片段</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    
    <script>
        let hlsPlayer = null;
        let currentStreamId = null;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${time}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            log(`状态: ${message}`);
        }
        
        async function startHLSStream() {
            const rtspUrl = document.getElementById('rtspUrl').value.trim();
            const streamId = document.getElementById('streamId').value.trim();
            
            if (!rtspUrl || !streamId) {
                showStatus('请输入RTSP地址和流ID', 'error');
                return;
            }
            
            showStatus('正在启动HLS流...', 'info');
            log(`启动流: ${streamId}`);
            
            try {
                const response = await fetch('/api/stream/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        rtspUrl: rtspUrl, 
                        streamId: streamId, 
                        format: 'hls' 
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('流启动成功，等待HLS片段生成...', 'success');
                    currentStreamId = streamId;
                    
                    // 等待HLS片段生成
                    setTimeout(() => {
                        initHLSPlayer(data.data);
                    }, 5000);
                    
                } else {
                    showStatus(`启动失败: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            }
        }
        
        function initHLSPlayer(streamInfo) {
            const video = document.getElementById('video');
            const hlsUrl = `http://localhost:3000${streamInfo.outputUrl}`;
            
            log(`初始化HLS播放器: ${hlsUrl}`);
            
            // 清理现有播放器
            if (hlsPlayer) {
                hlsPlayer.destroy();
                hlsPlayer = null;
            }
            
            if (Hls.isSupported()) {
                hlsPlayer = new Hls({
                    debug: false,
                    enableWorker: true,
                    lowLatencyMode: true,
                    backBufferLength: 90,
                    maxBufferLength: 30,
                    maxMaxBufferLength: 60,
                    liveSyncDurationCount: 3,
                    liveMaxLatencyDurationCount: 5
                });
                
                hlsPlayer.loadSource(hlsUrl);
                hlsPlayer.attachMedia(video);
                
                hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
                    log('HLS清单解析完成，开始播放');
                    showStatus('HLS播放器已启动，正在播放...', 'success');
                    video.play().catch(e => {
                        log(`播放失败: ${e.message}`);
                        showStatus('播放失败，请手动点击播放按钮', 'error');
                    });
                });
                
                hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
                    log(`HLS错误: ${data.type} - ${data.details}`);
                    if (data.fatal) {
                        showStatus('HLS播放致命错误', 'error');
                        switch(data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                log('网络错误，尝试恢复...');
                                hlsPlayer.startLoad();
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                log('媒体错误，尝试恢复...');
                                hlsPlayer.recoverMediaError();
                                break;
                            default:
                                log('无法恢复的错误');
                                break;
                        }
                    }
                });
                
                hlsPlayer.on(Hls.Events.FRAG_LOADED, () => {
                    log('新片段加载完成');
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                log('使用Safari原生HLS支持');
                video.src = hlsUrl;
                video.addEventListener('loadedmetadata', () => {
                    log('元数据加载完成，开始播放');
                    showStatus('原生HLS播放器已启动', 'success');
                    video.play();
                });
            } else {
                showStatus('浏览器不支持HLS播放', 'error');
                log('浏览器不支持HLS');
            }
        }
        
        async function stopStream() {
            if (!currentStreamId) {
                showStatus('没有活动的流', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/stream/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ streamId: currentStreamId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('流已停止', 'success');
                    log(`流 ${currentStreamId} 已停止`);
                    
                    // 清理播放器
                    if (hlsPlayer) {
                        hlsPlayer.destroy();
                        hlsPlayer = null;
                    }
                    
                    const video = document.getElementById('video');
                    video.src = '';
                    currentStreamId = null;
                    
                } else {
                    showStatus(`停止失败: ${data.error}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('页面加载完成');
            showStatus('准备就绪，请启动HLS流', 'info');
        };
    </script>
</body>
</html>
