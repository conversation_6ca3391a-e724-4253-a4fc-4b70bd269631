<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放现有HLS文件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        video {
            width: 100%;
            height: 500px;
            background: #000;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        button {
            padding: 12px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 播放现有HLS文件测试</h1>
        <p>直接播放之前生成的HLS文件，验证播放功能</p>
        
        <button onclick="playExistingHLS()">▶️ 播放现有HLS文件</button>
        <button onclick="checkFiles()">📁 检查文件状态</button>
        
        <div id="status"></div>
        
        <video id="video" controls>
            您的浏览器不支持视频播放
        </video>
        
        <div class="logs" id="logs">
            <div>📋 操作日志:</div>
        </div>
        
        <div class="info">
            <h3>📝 说明:</h3>
            <ul>
                <li>这个页面用于测试播放之前生成的HLS文件</li>
                <li>如果能正常播放，说明HLS生成和播放功能都正常</li>
                <li>如果不能播放，可能是文件损坏或播放器问题</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    
    <script>
        let hlsPlayer = null;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const time = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${time}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            log(`状态: ${message}`);
        }
        
        async function checkFiles() {
            log('检查HLS文件...');
            
            try {
                const response = await fetch('/hls/camera102/playlist.m3u8');
                if (response.ok) {
                    const content = await response.text();
                    log('播放列表内容:');
                    log(content);
                    showStatus('HLS文件检查成功', 'success');
                } else {
                    showStatus('HLS文件不存在', 'error');
                }
            } catch (error) {
                showStatus(`文件检查失败: ${error.message}`, 'error');
            }
        }
        
        function playExistingHLS() {
            const video = document.getElementById('video');
            const hlsUrl = '/hls/camera102/playlist.m3u8';
            
            log(`开始播放HLS文件: ${hlsUrl}`);
            showStatus('正在初始化播放器...', 'info');
            
            // 清理现有播放器
            if (hlsPlayer) {
                hlsPlayer.destroy();
                hlsPlayer = null;
            }
            
            if (Hls.isSupported()) {
                hlsPlayer = new Hls({
                    debug: true,
                    enableWorker: true
                });
                
                hlsPlayer.loadSource(hlsUrl);
                hlsPlayer.attachMedia(video);
                
                hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
                    log('HLS清单解析完成');
                    showStatus('开始播放...', 'success');
                    video.play().then(() => {
                        log('播放开始');
                        showStatus('播放成功！', 'success');
                    }).catch(e => {
                        log(`播放失败: ${e.message}`);
                        showStatus('播放失败，请手动点击播放按钮', 'error');
                    });
                });
                
                hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
                    log(`HLS错误: ${data.type} - ${data.details}`);
                    if (data.fatal) {
                        showStatus(`播放错误: ${data.details}`, 'error');
                    }
                });
                
                hlsPlayer.on(Hls.Events.FRAG_LOADED, (event, data) => {
                    log(`片段加载: ${data.frag.url}`);
                });
                
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Safari原生支持
                log('使用Safari原生HLS支持');
                video.src = hlsUrl;
                video.addEventListener('loadedmetadata', () => {
                    log('元数据加载完成');
                    showStatus('原生HLS播放器已启动', 'success');
                    video.play();
                });
                video.addEventListener('error', (e) => {
                    log(`视频错误: ${e.message}`);
                    showStatus('播放错误', 'error');
                });
            } else {
                showStatus('浏览器不支持HLS播放', 'error');
                log('浏览器不支持HLS');
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('页面加载完成');
            showStatus('准备就绪，可以播放现有HLS文件', 'info');
        };
    </script>
</body>
</html>
