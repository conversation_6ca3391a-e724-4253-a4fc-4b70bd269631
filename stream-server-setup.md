# 🎥 流媒体服务器配置指南

## 概述
要使用EasyPlayer播放海康摄像头视频，需要将RTSP流转换为浏览器支持的格式（FLV、HLS等）。

## 方案1：使用EasyDarwin（推荐）

### 1. 下载EasyDarwin
```bash
# Windows
下载地址：https://github.com/EasyDarwin/EasyDarwin/releases
下载 EasyDarwin-windows-xxx.zip

# Linux
wget https://github.com/EasyDarwin/EasyDarwin/releases/download/v8.1.0/EasyDarwin-linux-8.1.0-1901141151.tar.gz
tar -xzf EasyDarwin-linux-8.1.0-1901141151.tar.gz
```

### 2. 配置EasyDarwin
编辑 `easydarwin.ini` 配置文件：
```ini
[http]
port=8080
default_username=admin
default_password=admin

[rtsp]
port=554

[hls]
enable=true
fragment=5
window=3
```

### 3. 启动服务
```bash
# Windows
./easydarwin.exe

# Linux
./easydarwin
```

### 4. 推流配置
使用FFmpeg将RTSP推流到EasyDarwin：
```bash
ffmpeg -i rtsp://admin:Yf.31306@************:554/Streaming/Channels/101 \
       -c copy -f flv rtmp://localhost:1935/live/camera1
```

## 方案2：使用FFmpeg + Nginx-RTMP

### 1. 安装Nginx-RTMP
```bash
# Ubuntu/Debian
sudo apt-get install nginx-full
sudo apt-get install libnginx-mod-rtmp

# CentOS/RHEL
sudo yum install nginx
sudo yum install nginx-mod-rtmp
```

### 2. 配置Nginx
编辑 `/etc/nginx/nginx.conf`：
```nginx
rtmp {
    server {
        listen 1935;
        chunk_size 4096;
        
        application live {
            live on;
            
            # HLS配置
            hls on;
            hls_path /var/www/html/hls;
            hls_fragment 2s;
            hls_playlist_length 10s;
            
            # 允许推流
            allow publish all;
            allow play all;
        }
    }
}

http {
    server {
        listen 8080;
        
        location /hls {
            types {
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            root /var/www/html;
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
        }
        
        location /live {
            flv_live on;
            chunked_transfer_encoding on;
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Credentials true;
        }
    }
}
```

### 3. 启动服务
```bash
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 4. 推流到Nginx
```bash
ffmpeg -i rtsp://admin:Yf.31306@************:554/Streaming/Channels/101 \
       -c copy -f flv rtmp://localhost:1935/live/camera1
```

## 方案3：使用Node.js + node-media-server

### 1. 安装Node.js和依赖
```bash
npm init -y
npm install node-media-server
```

### 2. 创建服务器脚本
创建 `server.js`：
```javascript
const NodeMediaServer = require('node-media-server');

const config = {
  rtmp: {
    port: 1935,
    chunk_size: 60000,
    gop_cache: true,
    ping: 30,
    ping_timeout: 60
  },
  http: {
    port: 8000,
    allow_origin: '*'
  },
  relay: {
    ffmpeg: '/usr/local/bin/ffmpeg',
    tasks: [
      {
        app: 'live',
        mode: 'push',
        edge: 'rtsp://admin:Yf.31306@************:554/Streaming/Channels/101'
      }
    ]
  }
};

const nms = new NodeMediaServer(config);
nms.run();
```

### 3. 启动服务
```bash
node server.js
```

## 使用说明

### 流媒体地址格式
根据不同的服务器配置，生成的流媒体地址格式如下：

**EasyDarwin:**
- FLV: `http://服务器IP:8080/live/摄像头名称.flv`
- HLS: `http://服务器IP:8080/live/摄像头名称.m3u8`

**Nginx-RTMP:**
- FLV: `http://服务器IP:8080/live?app=live&stream=摄像头名称`
- HLS: `http://服务器IP:8080/hls/摄像头名称.m3u8`

**Node Media Server:**
- FLV: `http://服务器IP:8000/live/摄像头名称.flv`
- HLS: `http://服务器IP:8000/live/摄像头名称/index.m3u8`

### 推流命令示例
```bash
# 推流到EasyDarwin
ffmpeg -i rtsp://admin:Yf.31306@************:554/Streaming/Channels/101 \
       -c copy -f flv rtmp://localhost:1935/live/camera1

# 推流到Nginx-RTMP
ffmpeg -i rtsp://admin:Yf.31306@************:554/Streaming/Channels/101 \
       -c copy -f flv rtmp://localhost:1935/live/camera1

# 转换为HLS
ffmpeg -i rtsp://admin:Yf.31306@************:554/Streaming/Channels/101 \
       -c copy -f hls -hls_time 2 -hls_list_size 3 \
       /var/www/html/hls/camera1.m3u8
```

## 故障排除

### 常见问题
1. **连接超时**: 检查防火墙设置，确保端口开放
2. **推流失败**: 验证RTSP地址是否正确
3. **播放卡顿**: 调整码率和分辨率设置
4. **跨域问题**: 确保服务器配置了CORS头

### 测试方法
1. 使用VLC播放器测试RTSP地址
2. 使用FFplay测试流媒体地址
3. 检查服务器日志文件

## 性能优化

### 建议配置
- **分辨率**: 1920x1080或更低
- **帧率**: 25fps或更低
- **码率**: 2-4Mbps
- **编码**: H.264

### 服务器要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **网络**: 100Mbps以上带宽
- **存储**: SSD推荐
