<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTSP流测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .video-container {
            margin-top: 20px;
            text-align: center;
        }
        video {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #ffe7e7;
            color: #d00;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            background-color: #e7ffe7;
            color: #080;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>海康威视摄像头RTSP流测试</h1>
        
        <div class="info">
            <h3>使用说明：</h3>
            <p>1. 输入您的摄像头IP地址、用户名和密码</p>
            <p>2. 点击"生成RTSP地址"查看流地址</p>
            <p>3. 点击"测试连接"验证流是否可用</p>
            <p><strong>注意：</strong>由于浏览器安全限制，RTSP流无法直接在网页中播放，需要转换为其他格式</p>
        </div>

        <form id="rtspForm">
            <div class="form-group">
                <label for="cameraIP">摄像头IP地址:</label>
                <input type="text" id="cameraIP" placeholder="例如: *************" required>
            </div>
            
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" placeholder="摄像头密码" required>
            </div>
            
            <div class="form-group">
                <label for="port">RTSP端口:</label>
                <input type="text" id="port" value="554" required>
            </div>
            
            <button type="button" onclick="generateRTSP()">生成RTSP地址</button>
            <button type="button" onclick="testConnection()">测试连接</button>
        </form>

        <div id="rtspResult" style="margin-top: 20px;"></div>
        
        <div class="video-container">
            <h3>下一步：创建视频监控平台</h3>
            <p>确认RTSP流可用后，我们将创建一个完整的监控平台</p>
        </div>
    </div>

    <script>
        function generateRTSP() {
            const ip = document.getElementById('cameraIP').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const port = document.getElementById('port').value;
            
            if (!ip || !username || !password) {
                alert('请填写完整信息');
                return;
            }
            
            const mainStream = `rtsp://${username}:${password}@${ip}:${port}/Streaming/Channels/101`;
            const subStream = `rtsp://${username}:${password}@${ip}:${port}/Streaming/Channels/102`;
            
            const resultHTML = `
                <div class="success">
                    <h4>生成的RTSP地址：</h4>
                    <p><strong>主码流（高清）：</strong><br>
                    <code>${mainStream}</code></p>
                    <p><strong>子码流（标清）：</strong><br>
                    <code>${subStream}</code></p>
                    <p><small>请复制这些地址，稍后在监控平台中使用</small></p>
                </div>
            `;
            
            document.getElementById('rtspResult').innerHTML = resultHTML;
        }
        
        async function testConnection() {
            const ip = document.getElementById('cameraIP').value;
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!ip || !username || !password) {
                alert('请先填写完整信息');
                return;
            }
            
            // 测试摄像头Web接口是否可访问
            try {
                const testUrl = `http://${ip}/ISAPI/System/deviceInfo`;
                
                document.getElementById('rtspResult').innerHTML = `
                    <div class="info">
                        <p>正在测试连接到摄像头...</p>
                        <p>测试地址: ${testUrl}</p>
                        <p><strong>注意：</strong>由于跨域限制，可能无法直接测试。请手动验证以下内容：</p>
                        <ol>
                            <li>确认可以访问摄像头Web管理界面</li>
                            <li>确认RTSP服务已启用</li>
                            <li>确认用户名密码正确</li>
                        </ol>
                    </div>
                `;
                
                // 由于CORS限制，我们无法直接测试RTSP连接
                // 但可以提供验证步骤
                setTimeout(() => {
                    document.getElementById('rtspResult').innerHTML += `
                        <div class="success">
                            <h4>手动验证步骤：</h4>
                            <p>1. 使用VLC媒体播放器打开网络流</p>
                            <p>2. 输入RTSP地址进行测试</p>
                            <p>3. 如果能正常播放，说明RTSP流配置正确</p>
                        </div>
                    `;
                }, 1000);
                
            } catch (error) {
                document.getElementById('rtspResult').innerHTML = `
                    <div class="error">
                        <p>连接测试失败: ${error.message}</p>
                        <p>请检查IP地址和网络连接</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
