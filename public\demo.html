<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTSP转码演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .demo-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background: #4facfe;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            font-weight: bold;
            margin-right: 10px;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #4facfe;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2196F3;
            transform: translateY(-2px);
        }
        .architecture {
            text-align: center;
            margin: 20px 0;
            font-family: monospace;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
        }
        .flow-item {
            display: inline-block;
            padding: 10px 15px;
            margin: 5px;
            background: #e3f2fd;
            border-radius: 5px;
            border: 1px solid #2196F3;
        }
        .arrow {
            font-size: 20px;
            color: #2196F3;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 RTSP转码演示系统</h1>
            <p>基于FFmpeg的Web无插件RTSP视频流播放解决方案</p>
        </div>

        <div class="content">
            <!-- 系统架构 -->
            <div class="demo-section">
                <h3>🏗️ 系统架构</h3>
                <div class="architecture">
                    <div class="flow-item">RTSP摄像头</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">FFmpeg转码</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">HTTP-FLV/HLS</div>
                    <span class="arrow">→</span>
                    <div class="flow-item">Web播放器</div>
                </div>
                <p style="text-align: center; margin-top: 15px; color: #666;">
                    实时转码 • 低延迟 • 无插件 • 跨平台
                </p>
            </div>

            <!-- 安装状态检查 -->
            <div class="demo-section">
                <h3>🔧 系统状态检查</h3>
                <div id="systemStatus">
                    <div class="step">
                        <span class="step-number">1</span>
                        <strong>Node.js服务:</strong> <span id="nodeStatus">检查中...</span>
                    </div>
                    <div class="step">
                        <span class="step-number">2</span>
                        <strong>FFmpeg:</strong> <span id="ffmpegStatus">检查中...</span>
                    </div>
                    <div class="step">
                        <span class="step-number">3</span>
                        <strong>Web服务:</strong> <span id="webStatus">检查中...</span>
                    </div>
                </div>
            </div>

            <!-- FFmpeg安装指导 -->
            <div class="demo-section" id="ffmpegInstall" style="display: none;">
                <h3>⚠️ FFmpeg 安装指导</h3>
                <div class="alert alert-warning">
                    <strong>注意:</strong> 检测到FFmpeg未安装，这是转码的核心组件，必须安装后才能正常使用。
                </div>
                
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>Windows用户 - 使用Chocolatey安装（推荐）:</strong>
                    <div class="code">
                        # 以管理员身份打开PowerShell，运行：<br>
                        choco install ffmpeg
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>手动安装:</strong>
                    <div class="code">
                        1. 下载: https://ffmpeg.org/download.html<br>
                        2. 解压到 C:\ffmpeg<br>
                        3. 添加 C:\ffmpeg\bin 到系统PATH环境变量<br>
                        4. 重启命令行
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>验证安装:</strong>
                    <div class="code">ffmpeg -version</div>
                </div>

                <a href="INSTALL_FFMPEG.md" class="btn" target="_blank">📖 详细安装指南</a>
            </div>

            <!-- 使用演示 -->
            <div class="demo-section">
                <h3>🚀 使用演示</h3>
                
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>准备RTSP地址</strong>
                    <p>确保您有可用的RTSP摄像头地址，格式如下：</p>
                    <div class="code">
                        rtsp://username:password@ip:port/stream<br><br>
                        示例：<br>
                        • 海康威视: rtsp://admin:password@*************:554/Streaming/Channels/101<br>
                        • 大华: rtsp://admin:password@*************:554/cam/realmonitor?channel=1&subtype=0
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>启动转码服务</strong>
                    <p>通过API或Web界面启动RTSP转码：</p>
                    <div class="code">
                        POST /api/stream/start<br>
                        {<br>
                        &nbsp;&nbsp;"rtspUrl": "rtsp://your-camera-url",<br>
                        &nbsp;&nbsp;"streamId": "camera1",<br>
                        &nbsp;&nbsp;"format": "flv"<br>
                        }
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>Web播放</strong>
                    <p>在浏览器中播放转码后的视频流：</p>
                    <div class="code">
                        FLV: http://localhost:3000/live/camera1.flv<br>
                        HLS: http://localhost:3000/hls/camera1/playlist.m3u8
                    </div>
                </div>

                <a href="/test.html" class="btn">🧪 测试页面</a>
                <a href="/" class="btn">🎮 完整界面</a>
            </div>

            <!-- 技术特性 -->
            <div class="demo-section">
                <h3>✨ 技术特性</h3>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div class="step">
                        <strong>🚀 低延迟</strong>
                        <p>FLV格式延迟2-5秒，适合实时监控</p>
                    </div>
                    <div class="step">
                        <strong>🌐 无插件</strong>
                        <p>基于HTML5 Video + MSE，无需安装插件</p>
                    </div>
                    <div class="step">
                        <strong>📱 跨平台</strong>
                        <p>支持PC、移动端各种浏览器</p>
                    </div>
                    <div class="step">
                        <strong>🔧 易部署</strong>
                        <p>Docker一键部署，支持集群扩展</p>
                    </div>
                </div>
            </div>

            <!-- 格式对比 -->
            <div class="demo-section">
                <h3>📊 格式对比</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #dee2e6;">格式</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">延迟</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">兼容性</th>
                            <th style="padding: 12px; border: 1px solid #dee2e6;">适用场景</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><strong>FLV</strong></td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">2-5秒</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">现代浏览器</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">实时监控、直播</td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #dee2e6;"><strong>HLS</strong></td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">5-10秒</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">全平台</td>
                            <td style="padding: 12px; border: 1px solid #dee2e6;">移动端、录像回放</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="alert alert-success">
                <strong>🎉 恭喜！</strong> 您的RTSP Web播放器解决方案已经准备就绪。安装FFmpeg后即可开始使用！
            </div>
        </div>
    </div>

    <script>
        // 检查系统状态
        async function checkSystemStatus() {
            // 检查Node.js服务
            document.getElementById('nodeStatus').innerHTML = '<span style="color: green;">✓ 运行中</span>';
            
            // 检查Web服务
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('webStatus').innerHTML = '<span style="color: green;">✓ 正常</span>';
                    
                    if (data.ffmpeg === 'available') {
                        document.getElementById('ffmpegStatus').innerHTML = '<span style="color: green;">✓ 已安装</span>';
                    } else {
                        document.getElementById('ffmpegStatus').innerHTML = '<span style="color: red;">✗ 未安装</span>';
                        document.getElementById('ffmpegInstall').style.display = 'block';
                    }
                } else {
                    document.getElementById('webStatus').innerHTML = '<span style="color: red;">✗ 异常</span>';
                    document.getElementById('ffmpegStatus').innerHTML = '<span style="color: red;">✗ 未安装</span>';
                    document.getElementById('ffmpegInstall').style.display = 'block';
                }
            } catch (error) {
                document.getElementById('webStatus').innerHTML = '<span style="color: red;">✗ 连接失败</span>';
                document.getElementById('ffmpegStatus').innerHTML = '<span style="color: orange;">? 无法检测</span>';
            }
        }

        // 页面加载时检查状态
        window.onload = checkSystemStatus;
    </script>
</body>
</html>
