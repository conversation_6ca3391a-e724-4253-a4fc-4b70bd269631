<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EasyPlayer摄像头监控系统</title>
    <!-- 引入EasyPlayer.js -->
    <script src="https://cdn.jsdelivr.net/npm/@easydarwin/easyplayer@3.1.3/dist/component/EasyPlayer.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .config-panel {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
            font-size: 14px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #444;
            color: white;
            box-sizing: border-box;
        }
        .control-panel {
            background: #333;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .video-container {
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #555;
        }
        .video-header {
            background: #333;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }
        .video-player {
            width: 100%;
            height: 300px;
            background: #000;
        }
        .video-controls {
            background: #333;
            padding: 10px;
            text-align: center;
        }
        .video-controls button {
            padding: 8px 16px;
            margin: 2px;
            font-size: 12px;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .info-panel {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .url-display {
            background: #444;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 EasyPlayer摄像头监控系统</h1>
            <p>专业的网页视频播放解决方案</p>
        </div>

        <div class="info-panel">
            <h3>📋 使用说明</h3>
            <ul>
                <li><strong>支持格式：</strong>HTTP-FLV、HLS(m3u8)、WebSocket-FLV、WebRTC等</li>
                <li><strong>注意：</strong>EasyPlayer不能直接播放RTSP，需要先转换为支持的格式</li>
                <li><strong>推荐：</strong>使用EasyDarwin流媒体服务器将RTSP转换为FLV或HLS</li>
            </ul>
        </div>

        <div class="config-panel">
            <div class="form-group">
                <label>摄像头IP地址:</label>
                <input type="text" id="cameraIP" value="************">
            </div>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="password" value="Yf.31306">
            </div>
            <div class="form-group">
                <label>流媒体服务器地址:</label>
                <input type="text" id="streamServer" value="http://your-server:8080" placeholder="如: http://*************:8080">
            </div>
            <div class="form-group">
                <label>视频流格式:</label>
                <select id="streamFormat">
                    <option value="flv">HTTP-FLV</option>
                    <option value="hls">HLS (m3u8)</option>
                    <option value="ws-flv">WebSocket-FLV</option>
                </select>
            </div>
            <div class="form-group">
                <label>通道号:</label>
                <select id="channelId">
                    <option value="1">通道 1</option>
                    <option value="101" selected>通道 101</option>
                    <option value="102">通道 102</option>
                </select>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn-success" onclick="generateStreamUrl()">生成流媒体地址</button>
            <button onclick="addVideoPlayer()">添加播放器</button>
            <button onclick="startAllPlayers()">开始所有播放</button>
            <button class="btn-danger" onclick="stopAllPlayers()">停止所有播放</button>
            <button onclick="clearAllPlayers()">清空播放器</button>
        </div>

        <div id="streamUrlDisplay" style="display:none;">
            <div class="info-panel">
                <h3>🔗 生成的流媒体地址</h3>
                <div id="urlList"></div>
                <p><strong>注意：</strong>这些地址需要配合流媒体服务器使用</p>
            </div>
        </div>

        <div id="status" class="status info">请配置摄像头信息并生成流媒体地址</div>

        <div id="videoGrid" class="video-grid">
            <!-- 视频播放器将在这里动态添加 -->
        </div>

        <div class="info-panel">
            <h3>🛠️ 流媒体服务器配置指南</h3>
            <p><strong>方案1：使用EasyDarwin（推荐）</strong></p>
            <ol>
                <li>下载EasyDarwin: <code>https://github.com/EasyDarwin/EasyDarwin</code></li>
                <li>配置RTSP转FLV/HLS服务</li>
                <li>启动服务器</li>
                <li>使用生成的流媒体地址</li>
            </ol>
            
            <p><strong>方案2：使用FFmpeg + Nginx</strong></p>
            <ol>
                <li>安装Nginx + RTMP模块</li>
                <li>配置FFmpeg推流</li>
                <li>设置HLS输出</li>
            </ol>
        </div>
    </div>

    <script>
        let playerCount = 0;
        let players = {};

        function getConfig() {
            return {
                ip: document.getElementById('cameraIP').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value,
                streamServer: document.getElementById('streamServer').value,
                format: document.getElementById('streamFormat').value,
                channel: document.getElementById('channelId').value
            };
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function generateStreamUrl() {
            const config = getConfig();
            
            if (!config.ip || !config.username || !config.password) {
                alert('请填写完整的摄像头信息');
                return;
            }

            const rtspUrl = 'rtsp://' + config.username + ':' + config.password + '@' + config.ip + ':554/Streaming/Channels/' + config.channel;
            
            let streamUrls = [];
            
            if (config.format === 'flv') {
                streamUrls.push({
                    name: 'HTTP-FLV',
                    url: config.streamServer + '/live/' + config.ip + '_' + config.channel + '.flv'
                });
            } else if (config.format === 'hls') {
                streamUrls.push({
                    name: 'HLS (m3u8)',
                    url: config.streamServer + '/live/' + config.ip + '_' + config.channel + '.m3u8'
                });
            } else if (config.format === 'ws-flv') {
                streamUrls.push({
                    name: 'WebSocket-FLV',
                    url: 'ws://' + config.streamServer.replace('http://', '') + '/live/' + config.ip + '_' + config.channel + '.flv'
                });
            }

            let html = '<div class="url-display"><strong>原始RTSP地址:</strong><br>' + rtspUrl + '</div>';
            
            streamUrls.forEach(function(item) {
                html += '<div class="url-display"><strong>' + item.name + ':</strong><br>' + item.url + '</div>';
            });

            document.getElementById('urlList').innerHTML = html;
            document.getElementById('streamUrlDisplay').style.display = 'block';
            
            updateStatus('流媒体地址已生成，请确保流媒体服务器正在运行', 'info');
        }

        function addVideoPlayer() {
            const config = getConfig();
            
            if (!config.streamServer) {
                alert('请先配置流媒体服务器地址');
                return;
            }

            playerCount++;
            const playerId = 'player_' + playerCount;
            
            let streamUrl;
            if (config.format === 'flv') {
                streamUrl = config.streamServer + '/live/' + config.ip + '_' + config.channel + '.flv';
            } else if (config.format === 'hls') {
                streamUrl = config.streamServer + '/live/' + config.ip + '_' + config.channel + '.m3u8';
            } else if (config.format === 'ws-flv') {
                streamUrl = 'ws://' + config.streamServer.replace('http://', '') + '/live/' + config.ip + '_' + config.channel + '.flv';
            }

            const videoGrid = document.getElementById('videoGrid');
            const videoContainer = document.createElement('div');
            videoContainer.className = 'video-container';
            videoContainer.innerHTML = 
                '<div class="video-header">摄像头 ' + config.ip + ' - 通道 ' + config.channel + '</div>' +
                '<div id="' + playerId + '" class="video-player"></div>' +
                '<div class="video-controls">' +
                '<button onclick="playVideo(\'' + playerId + '\', \'' + streamUrl + '\')">播放</button>' +
                '<button onclick="pauseVideo(\'' + playerId + '\')">暂停</button>' +
                '<button onclick="stopVideo(\'' + playerId + '\')">停止</button>' +
                '<button onclick="removePlayer(\'' + playerId + '\')">移除</button>' +
                '</div>';

            videoGrid.appendChild(videoContainer);

            // 初始化EasyPlayer
            try {
                players[playerId] = new EasyPlayer({
                    id: playerId,
                    autoplay: false,
                    live: true,
                    hasAudio: true,
                    hasVideo: true,
                    stretch: false,
                    timeout: 10,
                    loadingText: '正在加载视频流...',
                    onerror: function(error) {
                        console.error('播放器错误:', error);
                        updateStatus('播放器错误: ' + error.message, 'error');
                    }
                });
                
                updateStatus('播放器 ' + playerCount + ' 已添加', 'success');
            } catch (error) {
                console.error('创建播放器失败:', error);
                updateStatus('创建播放器失败: ' + error.message, 'error');
            }
        }

        function playVideo(playerId, streamUrl) {
            if (players[playerId]) {
                try {
                    players[playerId].play(streamUrl);
                    updateStatus('开始播放: ' + playerId, 'success');
                } catch (error) {
                    console.error('播放失败:', error);
                    updateStatus('播放失败: ' + error.message, 'error');
                }
            }
        }

        function pauseVideo(playerId) {
            if (players[playerId]) {
                players[playerId].pause();
                updateStatus('已暂停: ' + playerId, 'info');
            }
        }

        function stopVideo(playerId) {
            if (players[playerId]) {
                players[playerId].stop();
                updateStatus('已停止: ' + playerId, 'info');
            }
        }

        function removePlayer(playerId) {
            if (players[playerId]) {
                players[playerId].destroy();
                delete players[playerId];
                
                const playerElement = document.getElementById(playerId);
                if (playerElement && playerElement.parentNode && playerElement.parentNode.parentNode) {
                    playerElement.parentNode.parentNode.remove();
                }
                
                updateStatus('播放器已移除: ' + playerId, 'info');
            }
        }

        function startAllPlayers() {
            const config = getConfig();
            let streamUrl;
            
            if (config.format === 'flv') {
                streamUrl = config.streamServer + '/live/' + config.ip + '_' + config.channel + '.flv';
            } else if (config.format === 'hls') {
                streamUrl = config.streamServer + '/live/' + config.ip + '_' + config.channel + '.m3u8';
            } else if (config.format === 'ws-flv') {
                streamUrl = 'ws://' + config.streamServer.replace('http://', '') + '/live/' + config.ip + '_' + config.channel + '.flv';
            }

            Object.keys(players).forEach(function(playerId) {
                playVideo(playerId, streamUrl);
            });
        }

        function stopAllPlayers() {
            Object.keys(players).forEach(function(playerId) {
                stopVideo(playerId);
            });
        }

        function clearAllPlayers() {
            Object.keys(players).forEach(function(playerId) {
                removePlayer(playerId);
            });
            document.getElementById('videoGrid').innerHTML = '';
            playerCount = 0;
            updateStatus('所有播放器已清空', 'info');
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            updateStatus('EasyPlayer监控系统已就绪', 'info');
        });
    </script>
</body>
</html>
