# FFmpeg 安装指南

## Windows 安装方法

### 方法一：使用 Chocolatey（推荐）

1. **安装 Chocolatey**（如果还没有安装）
   - 以管理员身份打开 PowerShell
   - 运行以下命令：
   ```powershell
   Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
   ```

2. **安装 FFmpeg**
   ```powershell
   choco install ffmpeg
   ```

3. **验证安装**
   ```powershell
   ffmpeg -version
   ```

### 方法二：手动安装

1. **下载 FFmpeg**
   - 访问：https://ffmpeg.org/download.html#build-windows
   - 选择 "Windows builds by BtbN"
   - 下载最新的 release 版本（例如：ffmpeg-master-latest-win64-gpl.zip）

2. **解压和配置**
   - 解压到 `C:\ffmpeg`
   - 将 `C:\ffmpeg\bin` 添加到系统环境变量 PATH 中

3. **添加到 PATH 环境变量**
   - 右键"此电脑" → "属性" → "高级系统设置"
   - 点击"环境变量"
   - 在"系统变量"中找到"Path"，点击"编辑"
   - 点击"新建"，添加：`C:\ffmpeg\bin`
   - 确定保存

4. **重启命令行**
   - 关闭所有命令行窗口
   - 重新打开 PowerShell 或 CMD

5. **验证安装**
   ```cmd
   ffmpeg -version
   ```

### 方法三：使用 Scoop

1. **安装 Scoop**
   ```powershell
   iwr -useb get.scoop.sh | iex
   ```

2. **安装 FFmpeg**
   ```powershell
   scoop install ffmpeg
   ```

## 其他操作系统

### macOS
```bash
# 使用 Homebrew
brew install ffmpeg
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg
```

### CentOS/RHEL
```bash
sudo yum install epel-release
sudo yum install ffmpeg
```

## 验证安装成功

安装完成后，在命令行中运行：
```bash
ffmpeg -version
```

如果看到版本信息输出，说明安装成功。

## 常见问题

### 1. 命令未找到
- 确保 FFmpeg 已正确添加到 PATH 环境变量
- 重启命令行窗口
- 重启计算机

### 2. 权限问题
- 以管理员身份运行命令行
- 检查防病毒软件是否阻止

### 3. 下载速度慢
- 使用国内镜像源
- 或者直接从官网下载预编译版本

## 安装完成后

1. 重启 RTSP Web 播放器服务
2. 访问 http://localhost:3000
3. 输入您的 RTSP 地址开始测试

## 技术支持

如果遇到安装问题，请：
1. 检查系统版本兼容性
2. 查看错误日志
3. 参考 FFmpeg 官方文档：https://ffmpeg.org/documentation.html
