const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const WebSocket = require('ws');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;
const STREAM_PORT = 8080;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 存储活动的流进程
const activeStreams = new Map();

// 配置信息
const config = {
    ffmpegPath: 'ffmpeg', // FFmpeg可执行文件路径
    outputFormats: ['flv', 'hls', 'm3u8'],
    defaultFormat: 'flv'
};

// 检查FFmpeg是否可用
function checkFFmpeg() {
    return new Promise((resolve, reject) => {
        const ffmpeg = spawn('ffmpeg', ['-version']);
        ffmpeg.on('close', (code) => {
            if (code === 0) {
                resolve(true);
            } else {
                reject(new Error('FFmpeg not found'));
            }
        });
        ffmpeg.on('error', (err) => {
            reject(err);
        });

        // 设置超时
        setTimeout(() => {
            ffmpeg.kill();
            reject(new Error('FFmpeg check timeout'));
        }, 5000);
    });
}

// 启动RTSP转码流
function startRTSPStream(rtspUrl, streamId, format = 'flv') {
    return new Promise((resolve, reject) => {
        // 如果流已经存在，先停止它
        if (activeStreams.has(streamId)) {
            stopStream(streamId);
        }

        let ffmpegArgs;
        let outputUrl;

        if (format === 'flv') {
            // 使用管道输出到HTTP流
            outputUrl = `/live/${streamId}.flv`;
            ffmpegArgs = [
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '3.0',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-f', 'flv',
                '-flvflags', 'no_duration_filesize',
                '-fflags', '+genpts',
                '-avoid_negative_ts', 'make_zero',
                'pipe:1'  // 输出到stdout
            ];
        } else if (format === 'hls') {
            // 确保HLS输出目录存在
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (!fs.existsSync(hlsDir)) {
                fs.mkdirSync(hlsDir, { recursive: true });
            }

            outputUrl = `/hls/${streamId}/playlist.m3u8`;
            ffmpegArgs = [
                '-i', rtspUrl,
                '-c:v', 'libx264',
                '-profile:v', 'baseline',
                '-level', '3.0',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-c:a', 'aac',
                '-ar', '44100',
                '-ac', '2',
                '-f', 'hls',
                '-hls_time', '2',           // 每个片段2秒
                '-hls_list_size', '6',      // 保持6个片段
                '-hls_flags', 'delete_segments+append_list',
                '-hls_allow_cache', '0',    // 禁用缓存
                '-hls_segment_filename', path.join(hlsDir, 'segment_%03d.ts'),
                path.join(hlsDir, 'playlist.m3u8')
            ];
        }

        console.log('启动FFmpeg进程:', ffmpegArgs.join(' '));

        const ffmpeg = spawn('ffmpeg', ffmpegArgs);
        
        ffmpeg.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });

        ffmpeg.stderr.on('data', (data) => {
            console.log(`FFmpeg stderr: ${data}`);
        });

        ffmpeg.on('close', (code) => {
            console.log(`FFmpeg进程退出，代码: ${code}`);
            activeStreams.delete(streamId);
        });

        ffmpeg.on('error', (err) => {
            console.error('FFmpeg错误:', err);
            activeStreams.delete(streamId);
            reject(err);
        });

        // 存储流信息
        activeStreams.set(streamId, {
            process: ffmpeg,
            rtspUrl: rtspUrl,
            format: format,
            outputUrl: outputUrl,
            startTime: new Date()
        });

        // 如果是FLV格式，设置管道处理
        if (format === 'flv') {
            // 将FFmpeg的stdout作为HTTP响应流
            ffmpeg.stdout.on('data', (data) => {
                // 广播数据到所有连接的客户端
                const stream = activeStreams.get(streamId);
                if (stream && stream.clients) {
                    stream.clients.forEach(client => {
                        if (!client.destroyed) {
                            client.write(data);
                        }
                    });
                }
            });

            // 初始化客户端列表
            const stream = activeStreams.get(streamId);
            if (stream) {
                stream.clients = [];
            }
        }

        // 等待一段时间确保流开始
        setTimeout(() => {
            resolve({
                streamId: streamId,
                outputUrl: outputUrl,
                format: format
            });
        }, 3000);
    });
}

// 停止流
function stopStream(streamId) {
    const stream = activeStreams.get(streamId);
    if (stream) {
        // 关闭所有客户端连接
        if (stream.clients) {
            stream.clients.forEach(client => {
                if (!client.destroyed) {
                    client.end();
                }
            });
        }

        stream.process.kill('SIGTERM');
        activeStreams.delete(streamId);

        // 清理HLS文件
        if (stream.format === 'hls') {
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (fs.existsSync(hlsDir)) {
                fs.rmSync(hlsDir, { recursive: true, force: true });
            }
        }

        return true;
    }
    return false;
}

// 实时FLV流路由
app.get('/live/:streamId.flv', (req, res) => {
    const streamId = req.params.streamId;
    const stream = activeStreams.get(streamId);

    if (!stream || stream.format !== 'flv') {
        return res.status(404).json({ error: '流不存在或格式不匹配' });
    }

    // 设置HTTP响应头
    res.writeHead(200, {
        'Content-Type': 'video/x-flv',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Range',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Connection': 'keep-alive'
    });

    // 将客户端添加到流的客户端列表
    if (!stream.clients) {
        stream.clients = [];
    }
    stream.clients.push(res);

    // 客户端断开连接时清理
    req.on('close', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });

    req.on('error', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });
});

// API路由

// 启动流
app.post('/api/stream/start', async (req, res) => {
    try {
        const { rtspUrl, streamId, format } = req.body;
        
        if (!rtspUrl || !streamId) {
            return res.status(400).json({ 
                error: '缺少必要参数: rtspUrl 和 streamId' 
            });
        }

        const result = await startRTSPStream(rtspUrl, streamId, format);
        res.json({
            success: true,
            message: '流启动成功',
            data: result
        });
    } catch (error) {
        console.error('启动流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 停止流
app.post('/api/stream/stop', (req, res) => {
    try {
        const { streamId } = req.body;
        
        if (!streamId) {
            return res.status(400).json({ 
                error: '缺少参数: streamId' 
            });
        }

        const stopped = stopStream(streamId);
        if (stopped) {
            res.json({
                success: true,
                message: '流停止成功'
            });
        } else {
            res.status(404).json({
                success: false,
                error: '流不存在'
            });
        }
    } catch (error) {
        console.error('停止流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取流状态
app.get('/api/stream/status', (req, res) => {
    const streams = Array.from(activeStreams.entries()).map(([id, stream]) => ({
        streamId: id,
        rtspUrl: stream.rtspUrl,
        format: stream.format,
        outputUrl: stream.outputUrl,
        startTime: stream.startTime,
        uptime: Date.now() - stream.startTime.getTime()
    }));

    res.json({
        success: true,
        data: {
            activeStreams: streams.length,
            streams: streams
        }
    });
});

// 健康检查
app.get('/api/health', async (req, res) => {
    try {
        await checkFFmpeg();
        res.json({
            success: true,
            message: 'Service is healthy',
            ffmpeg: 'available'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Service unhealthy',
            error: error.message
        });
    }
});

// 启动服务器
app.listen(PORT, async () => {
    console.log(`RTSP转码服务器运行在端口 ${PORT}`);
    
    try {
        await checkFFmpeg();
        console.log('✓ FFmpeg 可用');
    } catch (error) {
        console.error('✗ FFmpeg 不可用:', error.message);
        console.log('请确保已安装FFmpeg并添加到系统PATH中');
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('正在关闭服务器...');
    
    // 停止所有活动流
    for (const [streamId] of activeStreams) {
        stopStream(streamId);
    }
    
    process.exit(0);
});
